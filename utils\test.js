/**
 * @description 工具函数测试
 * <AUTHOR>
 * @date 2025-04-16
 */

// 测试错误处理系统
export function testErrorHandler() {
	try {
		const { logger, errorHandler } = require('./errorHandler.js')
		
		// 测试日志记录
		logger.info('Test log message')
		logger.warn('Test warning message')
		logger.error('Test error message')
		
		// 测试错误处理
		errorHandler.reportError(new Error('Test error'), 'Test Context')
		
		console.log('✅ Error handler test passed')
		return true
	} catch (error) {
		console.error('❌ Error handler test failed:', error)
		return false
	}
}

// 测试用户体验工具
export function testUserExperience() {
	try {
		const { FormValidator, validationRules } = require('./userExperience.js')
		
		// 测试表单验证
		const validator = new FormValidator()
		validator.addRule('name', validationRules.required('姓名必填'))
		validator.addRule('name', validationRules.minLength(2, '姓名至少2个字符'))
		
		// 测试验证
		const result1 = validator.validate({ name: '' })
		const result2 = validator.validate({ name: 'A' })
		const result3 = validator.validate({ name: '张三' })
		
		if (!result1.isValid && !result2.isValid && result3.isValid) {
			console.log('✅ User experience test passed')
			return true
		} else {
			console.error('❌ User experience test failed')
			return false
		}
	} catch (error) {
		console.error('❌ User experience test failed:', error)
		return false
	}
}

// 测试考试配置
export function testExamConfig() {
	try {
		const { examConfig, getScoreLevel } = require('./examConfig.js')
		
		// 测试评分等级
		const level1 = getScoreLevel(95)
		const level2 = getScoreLevel(75)
		const level3 = getScoreLevel(50)
		
		if (level1.label === '优秀' && level2.label === '中等' && level3.label === '不及格') {
			console.log('✅ Exam config test passed')
			return true
		} else {
			console.error('❌ Exam config test failed')
			return false
		}
	} catch (error) {
		console.error('❌ Exam config test failed:', error)
		return false
	}
}

// 测试数据分析
export function testDataAnalysis() {
	try {
		const { analyzeExamData } = require('./dataAnalysis.js')
		
		// 模拟考试数据
		const mockData = [
			{ name: '张三', department: '技术部', score: 90, submitTime: Date.now() },
			{ name: '李四', department: '技术部', score: 75, submitTime: Date.now() },
			{ name: '王五', department: '销售部', score: 60, submitTime: Date.now() }
		]
		
		const analysis = analyzeExamData(mockData)
		
		if (analysis.totalParticipants === 3 && analysis.averageScore === 75 && analysis.passRate === 100) {
			console.log('✅ Data analysis test passed')
			return true
		} else {
			console.error('❌ Data analysis test failed', analysis)
			return false
		}
	} catch (error) {
		console.error('❌ Data analysis test failed:', error)
		return false
	}
}

// 运行所有测试
export function runAllTests() {
	console.log('🧪 Running utility function tests...')
	
	const tests = [
		{ name: 'Error Handler', test: testErrorHandler },
		{ name: 'User Experience', test: testUserExperience },
		{ name: 'Exam Config', test: testExamConfig },
		{ name: 'Data Analysis', test: testDataAnalysis }
	]
	
	let passedTests = 0
	let totalTests = tests.length
	
	tests.forEach(({ name, test }) => {
		console.log(`\n📋 Testing ${name}...`)
		if (test()) {
			passedTests++
		}
	})
	
	console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`)
	
	if (passedTests === totalTests) {
		console.log('🎉 All tests passed! System is ready.')
		return true
	} else {
		console.log('⚠️ Some tests failed. Please check the implementation.')
		return false
	}
}

// 如果直接运行此文件，执行测试
if (typeof module !== 'undefined' && module.exports) {
	// Node.js 环境
	module.exports = {
		testErrorHandler,
		testUserExperience,
		testExamConfig,
		testDataAnalysis,
		runAllTests
	}
	
	// 如果直接运行
	if (require.main === module) {
		runAllTests()
	}
}

/**
 * @description 题库查看页面
 * <AUTHOR>
 * @date 2024-04-08
 */

<template>
	<view class="container">
		<view class="header">
			<text class="title">安全生产题库</text>
			<text class="subtitle">共{{questions.length}}道题目</text>
		</view>
		
		<!-- 返回按钮 -->
		<view class="back-btn" @click="goBack">
			<text>返回首页</text>
		</view>
		
		<!-- 搜索框 -->
		<view class="search-box">
			<input type="text" v-model="searchText" placeholder="搜索题目关键词" />
			<view class="search-icon" v-if="!searchText">🔍</view>
			<view class="clear-icon" v-else @click="clearSearch">✕</view>
		</view>
		
		<view class="question-list">
			<view class="question-card" v-for="(item, index) in filteredQuestions" :key="item.id">
				<view class="question-header">
					<text class="question-number">题目 {{index + 1}}</text>
				</view>
				<view class="question-content">
					<text class="question-text">{{item.question}}</text>
					<view class="options">
						<view v-for="(option, idx) in item.options" :key="idx" 
							  class="option" :class="{'correct': Number(idx) === Number(item.answer)}">
							<text>{{['A', 'B', 'C', 'D'][idx]}}. {{option}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 没有结果时的提示 -->
		<view class="no-result" v-if="searchText && filteredQuestions.length === 0">
			<text>没有找到与"{{searchText}}"相关的题目</text>
		</view>
	</view>
</template>

<script>
import { questionBank } from '@/utils/questionBank.js'

export default {
	data() {
		return {
			questions: [],
			searchText: ''
		}
	},
	computed: {
		filteredQuestions() {
			if (!this.searchText) return this.questions
			
			const keyword = this.searchText.toLowerCase()
			return this.questions.filter(q => 
				q.question.toLowerCase().includes(keyword) || 
				q.options.some(opt => opt.toLowerCase().includes(keyword))
			)
		}
	},
	onLoad() {
		this.loadQuestions()
	},
	methods: {
		loadQuestions() {
			this.questions = questionBank
		},
		clearSearch() {
			this.searchText = ''
		},
		goBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style>
.container {
	padding: 20rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.subtitle {
	font-size: 28rpx;
	color: #666;
}

.back-btn {
	background-color: #1E90FF;
	color: #fff;
	height: 70rpx;
	border-radius: 35rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
}

.search-box {
	position: relative;
	margin-bottom: 20rpx;
}

.search-box input {
	width: 100%;
	height: 80rpx;
	background-color: #fff;
	border-radius: 40rpx;
	padding: 0 70rpx 0 30rpx;
	font-size: 28rpx;
}

.search-icon, .clear-icon {
	position: absolute;
	right: 30rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 32rpx;
	color: #999;
}

.clear-icon {
	cursor: pointer;
}

.question-card {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.question-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.question-number {
	font-weight: bold;
	color: #333;
}

.question-content {
	padding: 10rpx;
}

.question-text {
	font-size: 32rpx;
	margin-bottom: 20rpx;
	line-height: 1.5;
}

.options {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.option {
	padding: 10rpx;
	border-radius: 8rpx;
	line-height: 1.5;
}

.option.correct {
	background-color: rgba(52, 199, 89, 0.1);
	color: #34C759;
}

.no-result {
	text-align: center;
	padding: 40rpx 0;
}

.no-result text {
	color: #999;
	font-size: 28rpx;
}
</style> 
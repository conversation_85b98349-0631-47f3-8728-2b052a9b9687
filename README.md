# 🛡️ 企业安全生产知识考试系统

<div align="center">

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Platform](https://img.shields.io/badge/platform-微信小程序-green.svg)
![Framework](https://img.shields.io/badge/framework-uni--app-orange.svg)
![Language](https://img.shields.io/badge/language-TypeScript-blue.svg)
![Version](https://img.shields.io/badge/version-v1.1.0-brightgreen.svg)

**一个专业的企业安全生产知识在线考试系统**

基于 uni-app 开发的现代化安全生产知识考试小程序，为企业员工提供便捷、高效的安全知识学习和考核平台。

[功能特点](#-功能特点) • [技术架构](#-技术架构) • [快速开始](#-快速开始) • [项目结构](#-项目结构) • [使用指南](#-使用指南)

</div>

---

## 📋 项目概述

本系统是一个功能完善的企业安全生产知识考试平台，采用现代化的技术架构，具备完善的错误处理、数据备份、性能优化等企业级功能。系统支持多角色使用，包括考生端和管理员端，能够满足企业安全培训和考核的各种需求。

### 🎯 核心价值

- **📚 知识普及**：通过在线考试提升员工安全意识
- **📊 数据分析**：全面的考试数据统计和分析
- **🔒 安全可靠**：完善的数据备份和安全机制
- **📱 移动优先**：基于微信小程序，使用便捷
- **⚡ 高性能**：优化的用户体验和系统性能

## ✨ 功能特点

### 👨‍💼 考生端功能

#### 📝 用户信息管理
- ✅ 姓名和部门信息录入与验证
- ✅ 信息本地持久化存储
- ✅ 支持信息修改更新
- ✅ 智能表单验证和错误提示

#### 📋 智能考试系统
- ✅ **丰富题库**：100+ 道精选安全生产题目
- ✅ **智能组卷**：按难度等级自动生成试卷
- ✅ **计时考试**：15分钟考试时长，实时倒计时
- ✅ **进度跟踪**：实时显示答题进度和剩余时间
- ✅ **灵活答题**：支持题目切换和答案修改
- ✅ **自动判分**：考试结束自动计算成绩
- ✅ **分类管理**：基础知识、法律法规、操作规程等分类

#### 📊 成绩分析系统
- ✅ 考试完成后即时查看得分
- ✅ 个人历史最佳成绩记录
- ✅ 实时排名和竞争分析
- ✅ 详细的答题统计和错题分析
- ✅ 正确答案对照和知识点解析

#### 📖 题库学习功能
- ✅ 完整题库浏览和学习
- ✅ 题目答案和详细解析
- ✅ 按分类和难度智能筛选
- ✅ 便于考前复习和知识巩固

### 👨‍💻 管理员功能

#### 🔐 安全认证系统
- ✅ 增强的管理员身份验证
- ✅ 安全的登录状态管理
- ✅ Token机制防止未授权访问
- ✅ 会话超时自动保护

#### 📈 考试数据管理
- ✅ 全员考试记录查看
- ✅ 按部门和时间筛选
- ✅ 考试成绩和用时统计
- ✅ 实时数据分析和可视化

#### 📤 数据导出功能
- ✅ **多格式支持**：CSV、Excel格式导出
- ✅ **详细报告**：包含完整的分析报告
- ✅ **数据完整**：考生信息和成绩数据
- ✅ **便捷操作**：一键复制到剪贴板

#### 💾 数据备份系统
- ✅ 自动定时备份机制
- ✅ 手动创建备份功能
- ✅ 备份数据恢复功能
- ✅ 备份文件导入/导出
- ✅ 存储空间智能管理

#### 📚 题库管理功能
- ✅ 完整题库查看和管理
- ✅ 题目分类和标签管理
- ✅ 难度等级设置和调整
- ✅ 答案解析编辑功能

## 🏗️ 技术架构

### 核心技术栈
- **🎨 前端框架**：uni-app (Vue 3)
- **💻 开发语言**：TypeScript + UTS
- **🎯 UI组件库**：uni-ui
- **📦 状态管理**：Vuex
- **💾 数据存储**：本地存储 + 备份系统
- **🛠️ 开发工具**：HBuilderX
- **📱 目标平台**：微信小程序

### 架构特性
- **🏗️ 模块化设计**：清晰的代码结构，易于维护扩展
- **🛡️ 错误处理系统**：完善的异常捕获和用户友好提示
- **⚡ 性能优化**：内置性能监控和优化机制
- **📱 响应式设计**：适配不同屏幕尺寸和设备
- **🔍 数据分析工具**：深度数据挖掘和统计分析

## 🚀 快速开始

### 环境要求

- **HBuilderX** 3.0+
- **微信开发者工具** 最新版
- **Node.js** 14.0+ (可选，用于包管理)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/164149043/examination.git
   cd examination
   ```

2. **使用 HBuilderX 打开项目**
   - 启动 HBuilderX
   - 选择"文件" → "打开目录"
   - 选择项目根目录

3. **配置微信小程序**
   - 在 `manifest.json` 中配置小程序 AppID
   - 确保微信开发者工具已安装并登录

4. **运行项目**
   ```bash
   # 在 HBuilderX 中
   运行 → 运行到小程序模拟器 → 微信开发者工具

   # 或者发布
   发行 → 小程序-微信
   ```

### 快速体验

1. **考生端体验**
   - 填写姓名和部门信息
   - 点击"开始考试"进行答题
   - 查看考试结果和排名

2. **管理员端体验**
   - 访问管理员登录页面
   - 默认账号：`admin`，密码：`123456`
   - 查看考试数据和管理功能

## 📁 项目结构

```
examination/
├── 📄 App.uvue                    # 应用入口文件
├── 📄 main.uts                    # 主入口文件
├── 📄 manifest.json               # 应用配置文件
├── 📄 pages.json                  # 页面路由配置
├── 📄 uni.scss                    # 全局样式文件
├── 📁 pages/                      # 页面文件目录
│   ├── 📁 index/                  # 🏠 首页（考试入口）
│   ├── 📁 examPaper/              # 📝 考试答题页面
│   ├── 📁 examResult/             # 📊 考试结果页面
│   ├── 📄 questionBank.uvue       # 📚 题库浏览页面
│   ├── 📁 score/                  # 🏆 成绩查询页面
│   ├── 📁 profile/                # 👤 个人信息页面
│   └── 📁 admin/                  # 👨‍💼 管理后台
│       ├── 📁 login/              # 🔐 管理员登录
│       ├── 📁 dashboard/          # 📈 管理后台首页
│       ├── 📁 questionBank/       # 📚 题库管理
│       ├── 📁 backupManager/      # 💾 备份管理
│       └── 📁 systemStatus/       # 🔧 系统状态
├── 📁 static/                     # 静态资源目录
│   ├── 📁 images/                 # 图片资源
│   └── 📄 logo.png                # 应用图标
├── 📁 utils/                      # 🛠️ 工具函数库
│   ├── 📄 questionBank.js         # 题库数据和管理
│   ├── 📄 examConfig.js           # 考试配置管理
│   ├── 📄 dataAnalysis.js         # 数据分析工具
│   ├── 📄 dataBackup.js           # 数据备份系统
│   ├── 📄 errorHandler.js         # 错误处理系统
│   ├── 📄 userExperience.js       # 用户体验优化
│   ├── 📄 performance.js          # 性能监控工具
│   └── 📄 systemInit.js           # 系统初始化
├── 📁 common/                     # 公共资源
│   └── 📄 responsive.scss         # 响应式样式
└── 📁 unpackage/                  # 编译输出目录
```

### 核心模块说明

| 模块 | 功能描述 | 主要文件 |
|------|----------|----------|
| **页面模块** | 用户界面和交互逻辑 | `pages/` |
| **工具模块** | 业务逻辑和数据处理 | `utils/` |
| **配置模块** | 应用配置和路由管理 | `manifest.json`, `pages.json` |
| **资源模块** | 静态资源和样式文件 | `static/`, `common/` |

## 📖 使用指南

### 👨‍💼 考生端使用流程

#### 1️⃣ 初始设置
- 📝 首次使用需填写个人信息（姓名和部门）
- ✅ 系统会自动验证信息格式
- 💾 信息将安全保存在本地

#### 2️⃣ 功能选择
在首页可以选择以下功能：

| 功能 | 描述 | 图标 |
|------|------|------|
| **开始考试** | 进入智能答题页面 | 📝 |
| **查看题库** | 浏览完整题库内容 | 📚 |
| **查看成绩** | 查看历史考试记录 | 🏆 |
| **个人信息** | 修改个人资料 | 👤 |

#### 3️⃣ 考试流程
- ⏰ **计时答题**：15分钟考试时长，实时倒计时
- 📊 **进度显示**：实时显示答题进度和剩余时间
- 🔄 **灵活操作**：支持题目切换和答案修改
- 🚀 **自动提交**：时间到达自动提交试卷

#### 4️⃣ 结果查看
考试完成后可以查看：
- 🎯 **考试得分**：即时显示考试成绩
- 📈 **排名情况**：与其他考生的排名对比
- 📋 **答案对照**：查看正确答案和详细解析
- 📊 **统计分析**：个人答题数据分析

### 👨‍💻 管理员使用流程

#### 1️⃣ 安全登录
- 🔐 访问管理员登录页面
- 👤 默认账号：`admin`
- 🔑 默认密码：`123456`
- ⚠️ **建议首次登录后立即修改密码**

#### 2️⃣ 管理功能

| 功能模块 | 主要功能 | 操作说明 |
|----------|----------|----------|
| **📊 数据分析** | 查看所有考试记录 | 支持按部门、时间筛选 |
| **📤 数据导出** | 导出考试数据 | 支持CSV、Excel格式 |
| **📚 题库管理** | 管理题库内容 | 查看、编辑题目信息 |
| **💾 备份管理** | 数据备份恢复 | 自动/手动备份功能 |
| **🔧 系统状态** | 监控系统运行 | 性能指标和错误日志 |

## 🏆 系统特性

### 🔧 技术特性

- **🏗️ 模块化架构**
  - 清晰的代码结构，易于维护和扩展
  - 组件化开发，提高代码复用性

- **🛡️ TypeScript支持**
  - 全面使用TypeScript，提供类型安全保障
  - 减少运行时错误，提升开发效率

- **⚡ 性能优化**
  - 内置性能监控和优化工具
  - 懒加载和缓存机制，确保流畅体验

- **📱 响应式设计**
  - 适配不同屏幕尺寸和设备
  - 优秀的移动端用户体验

### 📊 数据特性

- **🤖 智能备份**
  - 自动定时备份机制
  - 支持手动备份和一键恢复

- **📈 数据分析**
  - 深度数据挖掘和统计分析
  - 多维度报表和可视化图表

- **📤 导出功能**
  - 支持多种格式导出（CSV、Excel、PDF）
  - 自定义导出字段和格式

- **💾 存储优化**
  - 高效的本地存储管理
  - 支持大量数据的快速检索

### 🛡️ 安全特性

- **🔒 访问控制**
  - 多层级权限验证系统
  - 角色基础的访问控制（RBAC）

- **🔐 数据加密**
  - 敏感数据加密存储
  - 传输过程数据保护

- **📝 操作日志**
  - 完整的用户操作记录
  - 便于安全审计和问题追踪

- **🚨 错误监控**
  - 实时错误监控和告警
  - 快速定位和解决问题

## ⚠️ 注意事项

### 📋 使用须知

- ⚠️ **考试期间**：请勿刷新或关闭页面，避免数据丢失
- 👤 **用户识别**：同一用户（姓名和部门相同）多次考试只保留最新成绩
- 🌐 **网络环境**：确保网络连接稳定，避免考试过程中断
- 🔄 **定期备份**：建议管理员定期使用备份功能，防止数据丢失

### 🛠️ 开发须知

- 💻 **开发工具**：建议使用最新版本的 HBuilderX 和微信开发者工具
- 🔐 **安全设置**：管理员账号密码请及时修改，确保系统安全
- 📱 **兼容性**：已测试微信小程序平台，其他平台需要额外测试
- 🐛 **问题反馈**：遇到问题请通过 GitHub Issues 反馈

## 📝 更新日志

### 🎉 v1.1.0 (2025-04-16) - 企业级功能增强

#### 🚀 核心功能优化
- ✅ **智能组卷系统**：按难度等级自动生成试卷
- ✅ **表单验证增强**：更严格的数据验证和错误提示
- ✅ **实时反馈系统**：即时的用户操作反馈
- ✅ **性能监控**：内置性能分析和优化工具

#### 💾 数据管理系统
- ✅ **自动备份机制**：定时自动备份重要数据
- ✅ **备份恢复功能**：一键恢复历史备份数据
- ✅ **导入导出工具**：支持备份文件的导入导出
- ✅ **存储空间管理**：智能清理和空间优化

#### 📊 数据分析增强
- ✅ **多维度分析**：考试数据的深度统计分析
- ✅ **多格式导出**：支持 CSV、Excel、PDF 格式
- ✅ **部门统计**：按部门进行成绩统计和排名
- ✅ **趋势分析**：时间维度的数据趋势分析

#### 🛡️ 安全性提升
- ✅ **身份验证增强**：更安全的管理员登录机制
- ✅ **会话管理**：Token 机制和会话超时保护
- ✅ **操作审计**：完整的用户操作日志记录
- ✅ **错误监控**：实时错误捕获和告警系统

#### 🎨 用户体验优化
- ✅ **加载状态管理**：智能的加载提示和状态管理
- ✅ **友好错误提示**：用户友好的错误信息显示
- ✅ **操作确认**：重要操作的二次确认机制
- ✅ **响应式界面**：适配不同设备的界面设计

### 🎯 v1.0.0 (2025-04-16) - 基础版本

- ✅ 完成基础考试功能开发
- ✅ 实现管理员后台系统
- ✅ 支持考试成绩导出功能
- ✅ 完成题库管理基础功能

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

### 🔄 贡献流程

1. **Fork 项目**
   ```bash
   # 点击 GitHub 页面右上角的 Fork 按钮
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **提交更改**
   ```bash
   git commit -am 'Add some amazing feature'
   ```

4. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```

5. **创建 Pull Request**
   - 在 GitHub 上创建 Pull Request
   - 详细描述你的更改内容
   - 等待代码审查和合并

### 📋 贡献规范

- 🔍 **代码质量**：确保代码符合项目规范
- 📝 **提交信息**：使用清晰的提交信息
- 🧪 **测试覆盖**：为新功能添加相应测试
- 📚 **文档更新**：更新相关文档说明

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👨‍💻 作者信息

- **👤 作者**：张一依有把越女剑
- **📧 邮箱**：<EMAIL>
- **🐙 GitHub**：[@164149043](https://github.com/164149043)
- **💡 开发工具**：本项目完全由 Cursor AI 辅助开发

## 📸 项目截图

### 🎯 考生端界面

<div align="center">

| 考试首页 | 答题页面 |
|----------|----------|
| ![考试首页](./README_files/2.jpg) | ![答题页面](./README_files/3.jpg) |

</div>

### 👨‍💼 管理员端界面

<div align="center">

| 成绩管理 | 数据导出 |
|----------|----------|
| ![成绩管理](./README_files/5.jpg) | ![数据导出](./README_files/6.jpg) |

</div>

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给它一个星标！**

**🔗 [项目地址](https://github.com/164149043/examination) | 📚 [使用文档](#-使用指南) | 🐛 [问题反馈](https://github.com/164149043/examination/issues)**

</div>

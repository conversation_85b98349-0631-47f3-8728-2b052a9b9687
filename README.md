# 安全生产知识考试系统

一个基于 uni-app 开发的安全生产知识考试小程序，用于企业员工安全生产知识的在线考核。系统采用现代化的架构设计，具备完善的错误处理、数据备份、性能优化等企业级功能。

## 功能特点

### 考生端功能

- **用户信息管理**
  - 姓名和部门信息录入
  - 信息本地持久化存储
  - 支持信息修改更新
  - 智能表单验证

- **考试功能**
  - 100+题题库，按难度智能组卷
  - 考试时长15分钟，自动计时
  - 实时显示答题进度和剩余时间
  - 支持题目切换和修改答案
  - 自动判分和成绩统计
  - 题目分类管理（基础知识、法律法规、操作规程等）

- **成绩查看**
  - 考试完成后即时查看得分
  - 显示个人历史最好成绩
  - 显示当前排名情况
  - 详细的答题统计分析
  - 查看考试正确答案和解析

- **题库浏览**
  - 支持查看完整题库
  - 显示题目答案解析
  - 按分类和难度筛选
  - 便于考前复习

### 管理员功能

- **安全登录验证**
  - 增强的管理员账号密码验证
  - 安全的登录状态维护
  - Token机制防止未授权访问

- **考试记录管理**
  - 查看所有考生成绩
  - 支持按部门筛选
  - 显示考试时间和得分
  - 实时数据分析和统计

- **数据导出增强**
  - 支持导出CSV格式
  - 支持导出Excel格式
  - 导出详细分析报告
  - 包含考生信息和成绩数据
  - 一键复制到剪贴板

- **数据备份系统**
  - 自动备份功能
  - 手动创建备份
  - 备份恢复功能
  - 备份导入/导出
  - 存储空间管理

- **题库管理**
  - 查看完整题库
  - 题目分类管理
  - 难度等级设置
  - 正确答案显示

## 技术栈

- **前端框架**：uni-app
- **开发语言**：TypeScript、UTS
- **UI组件**：uni-ui
- **状态管理**：Vuex
- **数据存储**：本地存储 + 备份系统
- **开发工具**：HBuilderX
- **小程序平台**：微信小程序
- **架构特性**：
  - 模块化设计
  - 错误处理系统
  - 性能优化
  - 用户体验增强
  - 数据分析工具

## 安装和运行

1. 克隆项目
```bash
git clone https://github.com/164149043/examination.git
```

2. 安装依赖
```bash
npm install
# 或者使用 yarn
yarn install
```

3. 使用 HBuilderX 打开项目

4. 运行项目
- 在 HBuilderX 中选择"运行到小程序模拟器"
- 或选择"发行"，生成小程序代码

## 项目结构

```
├── pages                    # 页面文件
│   ├── index               # 首页（考试入口）
│   ├── examPaper           # 考试答题页面
│   ├── examResult         # 考试结果页面
│   ├── examList           # 考试列表页面
│   ├── examDetail         # 考试详情页面
│   ├── score              # 成绩查询页面
│   ├── profile            # 个人信息页面
│   ├── questionBank       # 题库浏览页面
│   └── admin              # 管理后台相关页面
│       ├── login          # 管理员登录
│       ├── dashboard      # 管理后台首页
│       ├── questionBank   # 题库管理页面
│       └── backupManager  # 备份管理页面
├── static                  # 静态资源
│   └── images             # 图片资源
├── utils                   # 工具函数库
│   ├── questionBank.js    # 题库数据和相关方法
│   ├── examConfig.js      # 考试配置和难度管理
│   ├── dataAnalysis.js    # 数据分析和报表工具
│   ├── dataBackup.js      # 数据备份和恢复系统
│   ├── errorHandler.js    # 错误处理和日志系统
│   ├── userExperience.js  # 用户体验增强工具
│   └── performance.js     # 性能优化工具
├── common                  # 公共资源
│   └── responsive.scss    # 响应式样式
└── App.uvue               # 应用入口文件
```

## 使用说明

### 考生使用流程
1. 首次使用填写个人信息（姓名和部门）
2. 可以在首页选择：
   - 开始考试：进入答题页面
   - 查看题库：浏览所有题目
   - 查看成绩：查看历史考试记录
3. 考试过程：
   - 答题时显示剩余时间和进度
   - 可以修改已答题目
   - 时间到自动提交
4. 完成考试后显示：
   - 考试得分
   - 排名情况
   - 正确答案对照

### 管理员使用流程
1. 通过管理员入口登录
   - 默认账号：admin
   - 默认密码：123456
2. 管理功能：
   - 查看所有考试记录
   - 导出考试数据
   - 管理题库内容

## 系统特性

### 🔧 技术特性

- **模块化架构**：采用模块化设计，代码结构清晰，易于维护
- **TypeScript支持**：全面使用TypeScript，提供类型安全保障
- **错误处理**：完善的错误捕获和处理机制，提升系统稳定性
- **性能优化**：内置性能监控和优化工具，确保流畅体验
- **响应式设计**：适配不同屏幕尺寸，支持多种设备

### 📊 数据特性

- **智能备份**：自动备份机制，支持手动备份和恢复
- **数据分析**：深度数据分析，提供多维度统计报表
- **导出功能**：支持多种格式导出，满足不同需求
- **存储优化**：高效的本地存储管理，支持大量数据

### 🛡️ 安全特性

- **访问控制**：多层级权限验证，保护敏感数据
- **数据加密**：重要数据加密存储，防止数据泄露
- **操作日志**：完整的操作记录，便于审计追踪
- **错误监控**：实时错误监控，快速定位问题

## 注意事项

- 考试过程中请勿刷新或关闭页面
- 同一用户（姓名和部门相同）多次考试只保留最新成绩
- 建议使用最新版本的微信开发者工具进行调试
- 确保网络环境稳定，避免考试中断
- 定期使用备份功能，防止数据丢失
- 管理员账号密码请及时修改，确保系统安全

## 更新日志

### v1.1.0 (2025-04-16) - 企业级功能增强

- **🚀 核心功能优化**
  - 智能题目组卷（按难度分布）
  - 增强的表单验证系统
  - 实时错误处理和用户反馈
  - 性能监控和优化

- **💾 数据管理系统**
  - 完整的数据备份和恢复功能
  - 自动备份机制
  - 备份导入/导出功能
  - 存储空间管理

- **📊 数据分析增强**
  - 详细的考试数据分析
  - 多格式数据导出（CSV、Excel、报告）
  - 部门统计和排名分析
  - 时间趋势分析

- **🛡️ 安全性提升**
  - 增强的管理员登录验证
  - Token机制和会话管理
  - 操作日志记录
  - 错误监控系统

- **🎨 用户体验优化**
  - 智能加载状态管理
  - 友好的错误提示
  - 操作确认对话框
  - 响应式界面设计

### v1.0.0 (2025-04-16) - 基础版本

- 完成基础考试功能
- 实现管理员后台
- 支持成绩导出
- 题库管理功能

## 贡献指南

1. Fork 本仓库
2. 创建新的分支 `git checkout -b feature/your-feature`
3. 提交更改 `git commit -am 'Add some feature'`
4. 推送到分支 `git push origin feature/your-feature`
5. 创建 Pull Request

## 版权信息

Copyright © 2025 张一依有把越女剑
（本项目完全由cursor开发制作）
## 联系方式

- 作者：张一依有把越女剑
- 邮箱：<EMAIL>
- GitHub：https://github.com/164149043

## 项目截图

### 考生端
![考试首页](./README_files/2.jpg)
![答题页面](./README_files/3.jpg)

### 管理员端
![成绩管理](./README_files/5.jpg)
![数据导出](./README_files/6.jpg)

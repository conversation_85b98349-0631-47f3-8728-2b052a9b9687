/**
 * @description 系统状态页面
 * <AUTHOR>
 * @date 2025-04-16
 */

<template>
	<view class="container">
		<!-- 顶部导航 -->
		<view class="nav-bar">
			<text class="back-btn" @click="goBack">← 返回</text>
			<text class="title">系统状态</text>
			<text class="refresh-btn" @click="refreshStatus">刷新</text>
		</view>
		
		<!-- 系统概览 -->
		<view class="overview-section">
			<view class="status-card" :class="getStatusClass(systemHealth.status)">
				<view class="status-icon">
					<text>{{ getStatusIcon(systemHealth.status) }}</text>
				</view>
				<view class="status-info">
					<text class="status-title">系统状态</text>
					<text class="status-value">{{ getStatusText(systemHealth.status) }}</text>
				</view>
				<view class="status-time">
					<text>{{ formatTime(systemHealth.timestamp) }}</text>
				</view>
			</view>
		</view>
		
		<!-- 详细检查结果 -->
		<view class="checks-section">
			<view class="section-title">
				<text>系统检查</text>
			</view>
			
			<view class="check-item" v-for="(check, key) in systemHealth.checks" :key="key">
				<view class="check-header">
					<view class="check-icon" :class="check.status ? 'success' : 'error'">
						<text>{{ check.status ? '✓' : '✗' }}</text>
					</view>
					<view class="check-info">
						<text class="check-name">{{ getCheckName(key) }}</text>
						<text class="check-message">{{ check.message }}</text>
					</view>
				</view>
				
				<view class="check-details" v-if="check.details">
					<view class="detail-item" v-for="(value, detailKey) in check.details" :key="detailKey">
						<text class="detail-label">{{ getDetailLabel(detailKey) }}:</text>
						<text class="detail-value">{{ formatDetailValue(detailKey, value) }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="actions-section">
			<view class="action-btn cleanup-btn" @click="performCleanup">
				<text>系统清理</text>
			</view>
			<view class="action-btn backup-btn" @click="createBackup">
				<text>创建备份</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { dataBackupManager } from '@/utils/dataBackup.js'
	import { logger } from '@/utils/miniprogramErrorHandler.js'
	import { messageManager, loadingManager } from '@/utils/userExperience.js'
	
	export default {
		data() {
			return {
				systemHealth: {
					timestamp: Date.now(),
					status: 'unknown',
					checks: {}
				}
			}
		},
		onLoad() {
			this.loadSystemStatus()
		},
		methods: {
			// 加载系统状态
			async loadSystemStatus() {
				try {
					// 简化的系统状态检查
					this.systemHealth = {
						timestamp: Date.now(),
						status: 'healthy',
						checks: {
							storage: this.checkStorage(),
							backup: this.checkBackupSystem()
						}
					}
					logger.info('System status loaded', this.systemHealth)
				} catch (error) {
					logger.error('Failed to load system status', error)
					messageManager.error('加载系统状态失败')
				}
			},

			// 检查存储系统
			checkStorage() {
				try {
					const testKey = 'system_health_test'
					const testValue = { timestamp: Date.now() }

					uni.setStorageSync(testKey, testValue)
					const retrieved = uni.getStorageSync(testKey)
					uni.removeStorageSync(testKey)

					const storageInfo = uni.getStorageInfoSync()

					return {
						status: true,
						message: 'Storage system working',
						details: {
							currentSize: storageInfo.currentSize,
							limitSize: storageInfo.limitSize,
							usage: Math.round((storageInfo.currentSize / storageInfo.limitSize) * 100)
						}
					}
				} catch (error) {
					return {
						status: false,
						message: 'Storage system error',
						error: error.message
					}
				}
			},

			// 检查备份系统
			checkBackupSystem() {
				try {
					const backupList = dataBackupManager.getBackupList()
					const storageInfo = dataBackupManager.getStorageInfo()

					return {
						status: true,
						message: 'Backup system working',
						details: {
							backupCount: backupList.length,
							lastBackup: backupList.length > 0 ? backupList[0].date : 'Never',
							storageUsage: storageInfo
						}
					}
				} catch (error) {
					return {
						status: false,
						message: 'Backup system error',
						error: error.message
					}
				}
			},
			
			// 刷新状态
			async refreshStatus() {
				loadingManager.show('refresh', { title: '正在刷新...' })
				
				try {
					await this.loadSystemStatus()
					messageManager.success('状态已刷新')
				} catch (error) {
					messageManager.error('刷新失败')
				} finally {
					loadingManager.hide('refresh')
				}
			},
			
			// 执行系统清理
			async performCleanup() {
				loadingManager.show('cleanup', { title: '正在清理系统...' })

				try {
					// 简化的系统清理
					this.cleanupLogs()
					this.cleanupTempData()

					messageManager.success('系统清理完成')
					await this.loadSystemStatus() // 刷新状态
				} catch (error) {
					logger.error('System cleanup failed', error)
					messageManager.error('系统清理失败')
				} finally {
					loadingManager.hide('cleanup')
				}
			},

			// 清理日志
			cleanupLogs() {
				try {
					const logs = uni.getStorageSync('app_logs') || []
					const maxLogs = 200

					if (logs.length > maxLogs) {
						const cleanedLogs = logs.slice(-maxLogs)
						uni.setStorageSync('app_logs', cleanedLogs)
						logger.info(`Cleaned up logs, kept ${cleanedLogs.length} entries`)
					}
				} catch (error) {
					logger.warn('Failed to cleanup logs', error)
				}
			},

			// 清理临时数据
			cleanupTempData() {
				try {
					const tempKeys = ['temp_', 'cache_', 'tmp_', 'system_health_test']
					const storageInfo = uni.getStorageInfoSync()

					storageInfo.keys.forEach(key => {
						if (tempKeys.some(prefix => key.startsWith(prefix))) {
							try {
								uni.removeStorageSync(key)
							} catch (error) {
								logger.warn(`Failed to remove temp key: ${key}`, error)
							}
						}
					})
				} catch (error) {
					logger.warn('Failed to cleanup temp data', error)
				}
			},
			
			// 创建备份
			async createBackup() {
				loadingManager.show('backup', { title: '正在创建备份...' })
				
				try {
					const result = await dataBackupManager.createFullBackup()
					if (result.success) {
						messageManager.success('备份创建成功')
						await this.loadSystemStatus() // 刷新状态
					} else {
						messageManager.error('备份创建失败')
					}
				} catch (error) {
					logger.error('Backup creation failed', error)
					messageManager.error('备份创建失败')
				} finally {
					loadingManager.hide('backup')
				}
			},
			
			// 获取状态样式类
			getStatusClass(status) {
				const classes = {
					'healthy': 'status-healthy',
					'warning': 'status-warning',
					'error': 'status-error'
				}
				return classes[status] || 'status-unknown'
			},
			
			// 获取状态图标
			getStatusIcon(status) {
				const icons = {
					'healthy': '✅',
					'warning': '⚠️',
					'error': '❌'
				}
				return icons[status] || '❓'
			},
			
			// 获取状态文本
			getStatusText(status) {
				const texts = {
					'healthy': '系统正常',
					'warning': '存在警告',
					'error': '系统异常'
				}
				return texts[status] || '状态未知'
			},
			
			// 获取检查项名称
			getCheckName(key) {
				const names = {
					'storage': '存储系统',
					'backup': '备份系统',
					'performance': '性能监控',
					'errorHandling': '错误处理'
				}
				return names[key] || key
			},
			
			// 获取详情标签
			getDetailLabel(key) {
				const labels = {
					'currentSize': '当前大小',
					'limitSize': '限制大小',
					'usage': '使用率',
					'backupCount': '备份数量',
					'lastBackup': '最后备份',
					'pageLoadTime': '页面加载时间',
					'averageApiResponseTime': '平均API响应时间',
					'memoryUsage': '内存使用',
					'errorCount': '错误数量'
				}
				return labels[key] || key
			},
			
			// 格式化详情值
			formatDetailValue(key, value) {
				if (key === 'usage') {
					return `${value}%`
				}
				if (key.includes('Time') || key.includes('time')) {
					return `${value}ms`
				}
				if (key.includes('Size') || key.includes('size') || key === 'memoryUsage') {
					return this.formatSize(value)
				}
				return value
			},
			
			// 格式化文件大小
			formatSize(bytes) {
				if (bytes === 0) return '0 B'
				const k = 1024
				const sizes = ['B', 'KB', 'MB', 'GB']
				const i = Math.floor(Math.log(bytes) / Math.log(k))
				return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
			},
			
			// 格式化时间
			formatTime(timestamp) {
				const date = new Date(timestamp)
				return date.toLocaleString('zh-CN')
			},
			
			// 返回
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}
	
	/* 导航栏 */
	.nav-bar {
		background-color: #1E90FF;
		padding: 20rpx 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.back-btn, .refresh-btn {
		font-size: 28rpx;
		color: #fff;
	}
	
	.title {
		font-size: 32rpx;
		color: #fff;
		font-weight: bold;
	}
	
	/* 系统概览 */
	.overview-section {
		padding: 30rpx;
	}
	
	.status-card {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}
	
	.status-healthy {
		border-left: 8rpx solid #52C41A;
	}
	
	.status-warning {
		border-left: 8rpx solid #FAAD14;
	}
	
	.status-error {
		border-left: 8rpx solid #FF4D4F;
	}
	
	.status-icon {
		font-size: 48rpx;
		margin-right: 20rpx;
	}
	
	.status-info {
		flex: 1;
	}
	
	.status-title {
		font-size: 26rpx;
		color: #666;
		display: block;
		margin-bottom: 8rpx;
	}
	
	.status-value {
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
	}
	
	.status-time {
		font-size: 24rpx;
		color: #999;
	}
	
	/* 检查结果 */
	.checks-section {
		padding: 0 30rpx;
	}
	
	.section-title {
		font-size: 30rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.check-item {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
	}
	
	.check-header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.check-icon {
		width: 40rpx;
		height: 40rpx;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
		font-size: 24rpx;
		color: #fff;
	}
	
	.check-icon.success {
		background-color: #52C41A;
	}
	
	.check-icon.error {
		background-color: #FF4D4F;
	}
	
	.check-info {
		flex: 1;
	}
	
	.check-name {
		font-size: 30rpx;
		color: #333;
		font-weight: bold;
		display: block;
		margin-bottom: 8rpx;
	}
	
	.check-message {
		font-size: 26rpx;
		color: #666;
	}
	
	.check-details {
		background-color: #f9f9f9;
		border-radius: 12rpx;
		padding: 20rpx;
	}
	
	.detail-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10rpx;
	}
	
	.detail-item:last-child {
		margin-bottom: 0;
	}
	
	.detail-label {
		font-size: 26rpx;
		color: #666;
	}
	
	.detail-value {
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
	}
	
	/* 操作按钮 */
	.actions-section {
		padding: 30rpx;
		display: flex;
		gap: 20rpx;
	}
	
	.action-btn {
		flex: 1;
		padding: 20rpx;
		border-radius: 40rpx;
		text-align: center;
		color: #fff;
		font-size: 28rpx;
	}
	
	.cleanup-btn {
		background-color: #FAAD14;
	}
	
	.backup-btn {
		background-color: #52C41A;
	}
</style>

/**
 * @description 响应式样式
 * <AUTHOR>
 * @date 2025-04-16
 */

// 屏幕尺寸断点
$breakpoints: (
  xs: 320px,   // 小屏手机
  sm: 375px,   // 中屏手机
  md: 414px,   // 大屏手机
  lg: 768px,   // 平板
  xl: 1024px   // 桌面
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 通用响应式类
.container-responsive {
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 30rpx;
  
  @include respond-to(md) {
    padding: 0 40rpx;
  }
  
  @include respond-to(lg) {
    max-width: 1200rpx;
    padding: 0 60rpx;
  }
}

// 字体大小响应式
.text-responsive {
  font-size: 28rpx;
  
  @include respond-to(sm) {
    font-size: 30rpx;
  }
  
  @include respond-to(md) {
    font-size: 32rpx;
  }
  
  @include respond-to(lg) {
    font-size: 34rpx;
  }
}

// 按钮响应式
.btn-responsive {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  
  @include respond-to(sm) {
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 30rpx;
  }
  
  @include respond-to(md) {
    height: 96rpx;
    border-radius: 48rpx;
    font-size: 32rpx;
  }
}

// 卡片响应式
.card-responsive {
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  @include respond-to(sm) {
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;
  }
  
  @include respond-to(lg) {
    border-radius: 24rpx;
    padding: 50rpx;
    margin-bottom: 40rpx;
  }
}

// 表格响应式
.table-responsive {
  overflow-x: auto;
  
  table {
    min-width: 600rpx;
    
    @include respond-to(lg) {
      min-width: 100%;
    }
  }
  
  th, td {
    padding: 16rpx 12rpx;
    font-size: 26rpx;
    
    @include respond-to(sm) {
      padding: 20rpx 16rpx;
      font-size: 28rpx;
    }
    
    @include respond-to(lg) {
      padding: 24rpx 20rpx;
      font-size: 30rpx;
    }
  }
}

// 网格布局响应式
.grid-responsive {
  display: grid;
  gap: 20rpx;
  grid-template-columns: 1fr;
  
  @include respond-to(sm) {
    grid-template-columns: repeat(2, 1fr);
    gap: 30rpx;
  }
  
  @include respond-to(lg) {
    grid-template-columns: repeat(3, 1fr);
    gap: 40rpx;
  }
}

// 弹性布局响应式
.flex-responsive {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  
  @include respond-to(sm) {
    flex-direction: row;
    gap: 30rpx;
  }
  
  .flex-item {
    flex: 1;
    min-width: 0;
  }
}

// 间距响应式
.spacing-responsive {
  padding: 20rpx;
  margin: 20rpx 0;
  
  @include respond-to(sm) {
    padding: 30rpx;
    margin: 30rpx 0;
  }
  
  @include respond-to(md) {
    padding: 40rpx;
    margin: 40rpx 0;
  }
  
  @include respond-to(lg) {
    padding: 50rpx;
    margin: 50rpx 0;
  }
}

// 图片响应式
.image-responsive {
  width: 100%;
  height: auto;
  border-radius: 12rpx;
  
  @include respond-to(sm) {
    border-radius: 16rpx;
  }
  
  @include respond-to(lg) {
    border-radius: 20rpx;
  }
}

// 输入框响应式
.input-responsive {
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  
  @include respond-to(sm) {
    height: 80rpx;
    padding: 0 24rpx;
    font-size: 30rpx;
    border-radius: 16rpx;
  }
  
  @include respond-to(md) {
    height: 88rpx;
    padding: 0 28rpx;
    font-size: 32rpx;
    border-radius: 20rpx;
  }
}

// 导航栏响应式
.navbar-responsive {
  height: 88rpx;
  padding: 0 30rpx;
  
  @include respond-to(sm) {
    height: 96rpx;
    padding: 0 40rpx;
  }
  
  @include respond-to(lg) {
    height: 104rpx;
    padding: 0 60rpx;
  }
  
  .nav-title {
    font-size: 32rpx;
    
    @include respond-to(sm) {
      font-size: 36rpx;
    }
    
    @include respond-to(lg) {
      font-size: 40rpx;
    }
  }
}

// 底部操作栏响应式
.action-bar-responsive {
  padding: 20rpx 30rpx;
  
  @include respond-to(sm) {
    padding: 30rpx 40rpx;
  }
  
  @include respond-to(lg) {
    padding: 40rpx 60rpx;
  }
}

// 隐藏/显示类
.hidden-xs {
  @media (max-width: map-get($breakpoints, xs)) {
    display: none !important;
  }
}

.hidden-sm {
  @media (max-width: map-get($breakpoints, sm)) {
    display: none !important;
  }
}

.visible-lg {
  display: none;
  
  @include respond-to(lg) {
    display: block;
  }
}

// 文本对齐响应式
.text-center-mobile {
  text-align: center;
  
  @include respond-to(sm) {
    text-align: left;
  }
}

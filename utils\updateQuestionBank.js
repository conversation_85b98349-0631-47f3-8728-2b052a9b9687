/**
 * @description 批量更新题库属性的工具脚本
 * <AUTHOR>
 * @date 2025-04-16
 */

// 为题库中的题目添加默认属性
export function updateQuestionBankAttributes(questionBank) {
	return questionBank.map((question, index) => {
		// 如果已经有属性，保持不变
		if (question.category && question.difficulty && question.explanation) {
			return question
		}
		
		// 根据题目ID分配难度和分类
		let category = '基础知识'
		let difficulty = 'medium'
		let explanation = '这是一道关于安全生产的基础知识题目。'
		
		// 根据题目内容智能分配分类
		const questionText = question.question.toLowerCase()
		
		if (questionText.includes('法') || questionText.includes('条例') || questionText.includes('规定')) {
			category = '法律法规'
		} else if (questionText.includes('操作') || questionText.includes('作业') || questionText.includes('使用')) {
			category = '操作规程'
		} else if (questionText.includes('应急') || questionText.includes('急救') || questionText.includes('事故')) {
			category = '应急处理'
		} else if (questionText.includes('防护') || questionText.includes('安全帽') || questionText.includes('安全带')) {
			category = '防护用品'
		}
		
		// 根据题目位置分配难度（简单的分配策略）
		if (index < 15) {
			difficulty = 'easy'
		} else if (index < 35) {
			difficulty = 'medium'
		} else {
			difficulty = 'hard'
		}
		
		// 生成简单的解释
		const correctOption = question.options[question.answer]
		explanation = `正确答案是"${correctOption}"。这是${category}相关的重要知识点。`
		
		return {
			...question,
			category,
			difficulty,
			explanation
		}
	})
}

// 导出更新后的题库数据（用于测试）
export function getUpdatedQuestionBank() {
	// 这里可以导入原始题库并更新
	// 由于循环依赖问题，这里只提供更新逻辑
	return []
}

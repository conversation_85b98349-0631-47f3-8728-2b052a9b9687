/**
 * @description 管理员登录页面
 * <AUTHOR>
 * @date 2025-04-16
 */

<template>
	<view class="container">
		<view class="login-form">
			<view class="title">
				<text>管理员登录</text>
			</view>
			
			<view class="input-group">
				<text class="label">账号</text>
				<input 
					class="input" 
					type="text" 
					v-model="username" 
					placeholder="请输入管理员账号"
					@keypress="clearErrorMsg"
				/>
			</view>
			
			<view class="input-group">
				<text class="label">密码</text>
				<input 
					class="input" 
					type="password" 
					v-model="password" 
					placeholder="请输入密码"
					@keypress="clearErrorMsg"
				/>
			</view>
			
			<view class="error-msg" v-if="errorMsg">
				<text>{{ errorMsg }}</text>
			</view>
			
			<view class="login-btn" :class="{'loading': loading}" @click="login">
				<text v-if="!loading">登录</text>
				<text v-else>登录中...</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				username: '',
				password: '',
				errorMsg: '',
				loading: false
			}
		},
		methods: {
			clearErrorMsg() {
				this.errorMsg = ''
			},
			// 获取管理员配置
			getAdminConfig() {
				// 可以从配置文件或环境变量中获取
				return {
					username: 'admin',
					password: '123456' // 建议使用加密存储
				}
			},
			// 生成登录token
			generateToken() {
				const timestamp = Date.now()
				const random = Math.random().toString(36).substr(2, 9)
				return `admin_${timestamp}_${random}`
			},
			login() {
				// 验证输入
				if (!this.username.trim()) {
					this.errorMsg = '请输入账号'
					return
				}
				
				if (!this.password.trim()) {
					this.errorMsg = '请输入密码'
					return
				}
				
				this.loading = true
				
				// 模拟网络请求延迟
				setTimeout(() => {
					// 从配置中获取管理员账号密码，增加安全性
					const adminConfig = this.getAdminConfig()
					if (this.username === adminConfig.username && this.password === adminConfig.password) {
						// 生成token并保存登录状态
						const token = this.generateToken()
						uni.setStorageSync('adminToken', token)
						uni.setStorageSync('adminLoginTime', Date.now())

						// 跳转到管理后台
						uni.redirectTo({
							url: '/pages/admin/dashboard'
						})
					} else {
						this.loading = false
						this.errorMsg = '账号或密码错误'
					}
				}, 800)
			}
		}
	}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.login-form {
		width: 100%;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
	}

	.title {
		text-align: center;
		margin-bottom: 40rpx;
	}

	.title text {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.input-group {
		margin-bottom: 30rpx;
	}

	.label {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
		display: block;
	}

	.input {
		width: 100%;
		height: 80rpx;
		background-color: #f5f5f5;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
	}
	
	.error-msg {
		padding: 10rpx 0;
		margin-bottom: 10rpx;
	}
	
	.error-msg text {
		color: #ff4d4f;
		font-size: 26rpx;
	}

	.login-btn {
		width: 100%;
		height: 88rpx;
		background-color: #1E90FF;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 20rpx;
		transition: all 0.3s;
	}
	
	.login-btn.loading {
		background-color: #89c0fa;
		opacity: 0.8;
	}

	.login-btn text {
		font-size: 32rpx;
		color: #fff;
	}
</style> 
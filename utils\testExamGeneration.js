/**
 * @description 测试考试生成功能
 * <AUTHOR>
 * @date 2025-04-16
 */

import { questionBank } from './questionBank.js'
import { generateExamByDifficulty } from './examConfig.js'

// 测试考试生成功能
export function testExamGeneration() {
	console.log('🧪 Testing exam generation...')
	
	try {
		// 测试题库是否正常
		console.log(`📚 Question bank loaded: ${questionBank.length} questions`)
		
		if (questionBank.length === 0) {
			console.error('❌ Question bank is empty!')
			return false
		}
		
		// 测试生成考试
		const questions = generateExamByDifficulty(questionBank, 10)
		console.log(`📝 Generated exam: ${questions.length} questions`)
		
		if (questions.length === 0) {
			console.error('❌ Failed to generate exam questions!')
			return false
		}
		
		// 检查题目属性
		let hasAllAttributes = true
		questions.forEach((q, index) => {
			if (!q.difficulty || !q.category) {
				console.error(`❌ Question ${index + 1} missing attributes:`, q)
				hasAllAttributes = false
			}
		})
		
		if (!hasAllAttributes) {
			console.error('❌ Some questions missing required attributes!')
			return false
		}
		
		// 统计难度分布
		const difficultyCount = {
			easy: 0,
			medium: 0,
			hard: 0
		}
		
		questions.forEach(q => {
			difficultyCount[q.difficulty]++
		})
		
		console.log('📊 Difficulty distribution:', difficultyCount)
		
		// 统计分类分布
		const categoryCount = {}
		questions.forEach(q => {
			categoryCount[q.category] = (categoryCount[q.category] || 0) + 1
		})
		
		console.log('📊 Category distribution:', categoryCount)
		
		console.log('✅ Exam generation test passed!')
		return true
		
	} catch (error) {
		console.error('❌ Exam generation test failed:', error)
		return false
	}
}

// 测试题库完整性
export function testQuestionBankIntegrity() {
	console.log('🔍 Testing question bank integrity...')
	
	try {
		let issues = []
		
		questionBank.forEach((q, index) => {
			// 检查必需字段
			if (!q.id) issues.push(`Question ${index + 1}: Missing id`)
			if (!q.question) issues.push(`Question ${index + 1}: Missing question`)
			if (!q.options || !Array.isArray(q.options)) issues.push(`Question ${index + 1}: Missing or invalid options`)
			if (q.answer === undefined || q.answer === null) issues.push(`Question ${index + 1}: Missing answer`)
			
			// 检查选项数量
			if (q.options && q.options.length !== 4) {
				issues.push(`Question ${index + 1}: Should have 4 options, has ${q.options.length}`)
			}
			
			// 检查答案索引
			if (q.options && (q.answer < 0 || q.answer >= q.options.length)) {
				issues.push(`Question ${index + 1}: Answer index ${q.answer} out of range`)
			}
		})
		
		if (issues.length > 0) {
			console.warn('⚠️ Question bank issues found:')
			issues.forEach(issue => console.warn(`  - ${issue}`))
		} else {
			console.log('✅ Question bank integrity check passed!')
		}
		
		return issues.length === 0
		
	} catch (error) {
		console.error('❌ Question bank integrity test failed:', error)
		return false
	}
}

// 运行所有测试
export function runAllTests() {
	console.log('🚀 Running all exam generation tests...')
	
	const tests = [
		{ name: 'Question Bank Integrity', test: testQuestionBankIntegrity },
		{ name: 'Exam Generation', test: testExamGeneration }
	]
	
	let passedTests = 0
	let totalTests = tests.length
	
	tests.forEach(({ name, test }) => {
		console.log(`\n📋 Testing ${name}...`)
		if (test()) {
			passedTests++
		}
	})
	
	console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`)
	
	if (passedTests === totalTests) {
		console.log('🎉 All tests passed! Exam generation is working correctly.')
		return true
	} else {
		console.log('⚠️ Some tests failed. Please check the implementation.')
		return false
	}
}

// 如果在浏览器环境中，可以通过控制台调用
if (typeof window !== 'undefined') {
	window.testExamGeneration = runAllTests
}
